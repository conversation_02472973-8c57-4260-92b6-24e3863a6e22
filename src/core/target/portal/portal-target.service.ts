import { Inject, Injectable } from '@nestjs/common';
import { Target, TargetMeta } from '@core/sync/target.interface';
import { Candidate, Job } from '@core/sync/model';
import { PORTAL_JOB_SERVICE, PortalJobService } from '@core/target/portal/job/portal-job-service.interface';
import {
  PORTAL_CANDIDATE_SERVICE,
  PortalCandidateService,
} from '@core/target/portal/candidate/portal-candidate-service.interface';
import { Source } from '@core/sync/source.interface';
import { ConfigService } from '@nestjs/config';
import * as portal from '@EmmySoft-GmbH/emmysoft-client';
import { EnvConfig, TenantConfig } from '@core/env.model';

export const PORTAL_TARGET_SERVICE = 'PORTAL_TARGET_SERVICE';

export const PORTAL_SOURCE_META_NAME = 'portal';

@Injectable()
export class PortalTargetServiceImpl implements Target {
  constructor(
    @Inject(PORTAL_JOB_SERVICE) private readonly jobService: PortalJobService,
    @Inject(PORTAL_CANDIDATE_SERVICE)
    private readonly candidateService: PortalCandidateService,
    private readonly config: ConfigService<EnvConfig>,
  ) {}

  isConfigured(): boolean {
    return this.config.get<Array<TenantConfig>>('tenants').some(c => c.portal?.uri);
  }

  getTargetMeta(): TargetMeta {
    return new TargetMeta(
      PORTAL_SOURCE_META_NAME,
      this.config
        .get<Array<TenantConfig>>('tenants')
        .map(t => t.portal?.uri)
        .join(','),
    );
  }

  async initWithTargetJob(job: Job, source: Source, tenant: TenantConfig): Promise<void> {
    job.target = await this.jobService.findByReferenceKey(source, job.id, tenant);
  }

  async importJob(job: Job, source: Source, tenant: TenantConfig, options?: Record<string, any>): Promise<void> {
    const existingJob: portal.Job = await this.jobService.findByReferenceKey(source, job.id, tenant);
    if (existingJob) {
      job.target = existingJob;
      return await this.jobService.updateJob(job, source, tenant);
    }
    await this.jobService.createJob(job, source, tenant);
  }

  async deleteJob(job: Job, source: Source, tenant: TenantConfig): Promise<any> {
    await this.jobService.deleteJob(job, source, tenant);
  }

  async importCandidate(
    candidate: Candidate,
    job: Job,
    source: Source,
    tenant: TenantConfig,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    options?: Record<string, any>,
  ): Promise<void> {
    await this.candidateService.createOrUpdateCandidate(candidate, job, source, tenant);
  }

  async deleteCandidate(candidate: Candidate, source: Source, tenant: TenantConfig): Promise<void> {
    await this.candidateService.deleteCandidate(candidate, source, tenant);
  }
}
