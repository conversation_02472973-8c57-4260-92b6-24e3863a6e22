import { Inject, Injectable } from '@nestjs/common';
import { Target, TargetMeta } from '@core/sync/target.interface';
import { Source } from '@core/sync/source.interface';
import { Candidate, Job } from '@core/sync/model';
import { ConfigService } from '@nestjs/config';
import { SCORE_JOB_SERVICE, ScoreJobService } from '@core/target/score/job/score-job-service.interface';
import {
  SCORE_PARTICIPATION_SERVICE,
  ScoreParticipationService,
} from '@core/target/score/participation/score-participation-service.interface';
import { EnvConfig, TenantConfig } from '@core/env.model';
import { EnhancedLoggerService } from '@infrastructure/logger/enhanced-logger.service';

export const SCORE_TARGET_SERVICE = 'SCORE_TARGET_SERVICE';

export const SCORE_SOURCE_META_NAME = 'score';

@Injectable()
export class ScoreTargetServiceImpl implements Target {
  constructor(
    private readonly config: ConfigService<EnvConfig>,
    private readonly logger: EnhancedLoggerService,
    @Inject(SCORE_JOB_SERVICE) private readonly jobService: ScoreJobService,
    @Inject(SCORE_PARTICIPATION_SERVICE)
    private readonly participationService: ScoreParticipationService,
  ) {
    this.logger.setContext(ScoreTargetServiceImpl.name);
  }

  isConfigured(): boolean {
    return true;
  }

  getTargetMeta(): TargetMeta {
    return new TargetMeta(
      SCORE_SOURCE_META_NAME,
      this.config
        .get<Array<TenantConfig>>('tenants')
        .map(t => t.score.uri)
        .join(','),
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  initWithTargetJob(job: Job, source: Source, tenant: TenantConfig): Promise<void> {
    // do nothing
    return Promise.resolve(undefined);
  }

  async importJob(job: Job, source: Source, tenant: TenantConfig, options?: Record<string, any>): Promise<any> {
    return await this.jobService.importJob(job, source, tenant);
  }

  async deleteJob(job: Job, source: Source, tenant: TenantConfig): Promise<any> {
    return await this.jobService.deleteJob(job, source, tenant);
  }

  async importCandidate(
    candidate: Candidate,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    job: Job,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    source: Source,
    tenant: TenantConfig,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    options?: Record<string, any>,
  ): Promise<any> {
    const { data } = await this.participationService.importParticipation(candidate, tenant);
    return data;
  }

  async deleteCandidate(candidate: Candidate, source: Source, tenant: TenantConfig): Promise<any> {
    return await this.participationService.deleteParticipation(candidate, tenant);
  }
}
