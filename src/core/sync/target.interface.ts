import { Job } from '@core/sync/model/job.model';
import { Candidate } from '@core/sync/model/candidate.model';
import { Source } from '@core/sync/source.interface';
import { TenantConfig } from '@core/env.model';

export class TargetMeta {
  constructor(
    /**
     * Source name.
     */
    public readonly name: string,
    public readonly uri: string,
  ) {}
}

export interface Target {
  getTargetMeta(): TargetMeta;

  // used for events when importing candidate
  initWithTargetJob(job: Job, source: Source, tenant: TenantConfig): Promise<void>;

  importJob(job: Job, source: Source, tenant: TenantConfig, options?: Record<string, any>): Promise<any>;

  deleteJob(job: Job, source: Source, tenant: TenantConfig): Promise<void>;

  importCandidate(
    candidate: Candidate,
    job: Job,
    source: Source,
    tenant: TenantConfig,
    options?: Record<string, any>,
  ): Promise<any>;

  deleteCandidate(candidate: Candidate, source: Source, tenant: TenantConfig): Promise<void>;

  /**
   * Returns true if the target is configured with all necessary parameters (env variables, etc.).
   * Used for silent skipping of module usage.
   */
  isConfigured(): boolean;
}
