import { Test, TestingModule } from '@nestjs/testing';
import { createMock, DeepMocked } from '@golevelup/ts-jest';
import { EventsServiceImpl } from '@core/events/events.service';
import { SOFTFACTORS_SOURCE_SERVICE } from '@core/source/softfactors/softfactors-source.service';
import { Source } from '@core/sync/source.interface';
import { SCORE_TARGET_SERVICE } from '@core/target/score/score-target.service';
import { Target } from '@core/sync/target.interface';
import { TENANT_SERVICE, TenantService } from '@core/tenant/tenant-service.interface';
import { ERROR_REPORTER, ErrorReporter } from '@core/error-reporter.interface';
import { EventData, EventKind, EventMessage } from '@core/events/event-data.model';
import { EmptyLogger } from '@infrastructure/test-util/empty-logger';
import { SoftfactorsException } from '@core/source/softfactors/softfactors.exception';
import { AxiosError, AxiosResponse } from 'axios';
import { TenantConfig } from '@core/env.model';
import { Candidate } from '@core/sync/model';
import { EnhancedLoggerService } from '@infrastructure/logger/enhanced-logger.service';
import { PORTAL_V4_TARGET_SERVICE } from '@core/target/portal-v4/portal-v4-target.service';
import { PORTAL_TARGET_SERVICE } from '@core/target/portal/portal-target.service';

describe('EventsService', () => {
  let service: EventsServiceImpl;
  let source: DeepMocked<Source>;
  let scoreTarget: DeepMocked<Target>;
  let portalTarget: DeepMocked<Target>;
  let errorReporter: DeepMocked<ErrorReporter>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EventsServiceImpl,
        {
          provide: EnhancedLoggerService,
          useValue: createMock<EnhancedLoggerService>(),
        },
        {
          provide: SOFTFACTORS_SOURCE_SERVICE,
          useValue: createMock<Source>(),
        },
        {
          provide: SCORE_TARGET_SERVICE,
          useValue: createMock<Target>(),
        },
        {
          provide: PORTAL_V4_TARGET_SERVICE,
          useValue: createMock<Target>(),
        },
        {
          provide: TENANT_SERVICE,
          useValue: createMock<TenantService>(),
        },
        {
          provide: ERROR_REPORTER,
          useValue: createMock<ErrorReporter>(),
        },
      ],
    }).compile();
    module.useLogger(new EmptyLogger());

    service = module.get<EventsServiceImpl>(EventsServiceImpl);
    source = module.get(SOFTFACTORS_SOURCE_SERVICE);
    scoreTarget = module.get(SCORE_TARGET_SERVICE);
    portalTarget = module.get(PORTAL_V4_TARGET_SERVICE);
    errorReporter = module.get(ERROR_REPORTER);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should handle unknown error with error reporter', async () => {
    const message = new EventMessage();
    message.eventKind = EventKind.PositionCreated;

    const eventData = new EventData(null, null, message);

    const error = new Error('failed to load job details');
    source.getJobDetails.mockRejectedValue(error);

    const result = service.handleEvent(eventData);
    await expect(result).rejects.toBe(error);
    expect(errorReporter.report).toHaveBeenCalled();
  });

  it('should handle softfactors error with error reporter', async () => {
    const message = new EventMessage();
    message.id = 'JOPG_test_1';
    message.eventKind = EventKind.PositionCreated;

    const eventData = new EventData(null, null, message);

    const error = new SoftfactorsException(
      'failed to load job details',
      'job import',
      new AxiosError('failed to load', '500', null, null, {} as AxiosResponse),
    );
    source.getJobDetails.mockRejectedValue(error);

    const result = service.handleEvent(eventData);
    await expect(result).rejects.toBe(error);
    expect(errorReporter.report).toHaveBeenCalled();
  });

  it('should properly handle position event (events starting with Position)', async () => {
    const message = new EventMessage();
    message.id = 'JOPG_test_1';
    message.eventKind = EventKind.PositionCreated;
    message.tenantId = 'test';

    const eventData = new EventData('moonwalk', 'test', message);

    const result = service.handleEvent(eventData);

    await expect(result).resolves.toBeUndefined();
    expect(source.getJobDetails).toHaveBeenCalled();
    expect(scoreTarget.importJob).toHaveBeenCalled();
  });

  it('should properly handle participation event (events starting with Participation)', async () => {
    const message = new EventMessage();
    message.id = 'PCPN_test_1';
    message.jobOpeningId = 'JOPG_test_1';
    message.eventKind = EventKind.ParticipationUpdated;
    message.tenantId = 'test';

    const eventData = new EventData('moonwalk', 'test', message);

    const result = service.handleEvent(eventData);

    await expect(result).resolves.toBeUndefined();
    expect(source.getJobDetails).toHaveBeenCalled();
    expect(source.getCandidateDetails).toHaveBeenCalled();
    expect(scoreTarget.initWithTargetJob).toHaveBeenCalled();
    expect(scoreTarget.importCandidate).toHaveBeenCalled();
  });

  it('should import job to score target', async () => {
    const message = new EventMessage();
    message.id = 'JOPG_test_1';
    const tenant: TenantConfig = { id: 1, name: 'test' } as TenantConfig;

    source.getJobDetails.mockResolvedValue({ id: 'test-job', source: {} });
    scoreTarget.importJob.mockResolvedValue({});
    portalTarget.isConfigured.mockReturnValue(false);

    await service.handlePositionEvent(tenant, message);

    expect(source.getJobDetails).toHaveBeenCalledWith(message.id, tenant);
    expect(scoreTarget.importJob).toHaveBeenCalled();
    expect(portalTarget.importJob).not.toHaveBeenCalled();
  });

  it('should import job to score only and skip portal target even if portal is configured', async () => {
    const message = new EventMessage();
    message.id = 'JOPG_test_1';
    const tenant: TenantConfig = { id: 1, name: 'test' } as TenantConfig;

    source.getJobDetails.mockResolvedValue({ id: 'test-job', source: {} });
    scoreTarget.importJob.mockResolvedValue({});
    portalTarget.isConfigured.mockReturnValue(true);
    portalTarget.importJob.mockResolvedValue({});

    await service.handlePositionEvent(tenant, message);

    expect(source.getJobDetails).toHaveBeenCalledWith(message.id, tenant);
    expect(scoreTarget.importJob).toHaveBeenCalled();
    expect(portalTarget.importJob).not.toHaveBeenCalled();
  });

  it('should import job to both score and portal targets when portal is configured and portalProjectId is returned', async () => {
    // Arrange
    const message = new EventMessage();
    message.id = 'JOPG_test_1';
    message.eventKind = EventKind.PositionCreated;
    const tenant: TenantConfig = { id: 1, name: 'test' } as TenantConfig;

    // Mock job details from source
    const jobDetails = { id: 'test-job', source: {} };
    source.getJobDetails.mockResolvedValue(jobDetails);

    // Mock score target response with portalProjectId
    const scoreJobResponse = {
      portalProjectId: 'portal-project-123',
      jobOpening: {
        id: 'score-job-456',
        softfactorsData: {
          id: 'softfactors-job-789'
        }
      }
    };
    scoreTarget.importJob.mockResolvedValue(scoreJobResponse);

    // Mock portal is configured
    portalTarget.isConfigured.mockReturnValue(true);
    portalTarget.importJob.mockResolvedValue({});

    // Act
    await service.handlePositionEvent(tenant, message);

    // Assert
    expect(source.getJobDetails).toHaveBeenCalledWith(message.id, tenant);
    expect(scoreTarget.importJob).toHaveBeenCalledWith(jobDetails, source, tenant, message);
    expect(portalTarget.importJob).toHaveBeenCalledWith(jobDetails, source, tenant, message, {
      portalProjectId: 'portal-project-123',
      scoreProjectIdId: 'score-job-456',
      softfactorsJobOpeningId: 'softfactors-job-789',
    });
  });

  it('should import candidate to score target', async () => {
    const message = new EventMessage();
    message.id = 'PCPN_test_1';
    message.jobOpeningId = 'JOPG_test_1';
    const tenant: TenantConfig = { id: 1, name: 'test' } as TenantConfig;

    source.getJobDetails.mockResolvedValue({ id: 'test-job', source: {} });
    source.getCandidateDetails.mockResolvedValue(new Candidate('test-candidate'));
    scoreTarget.importCandidate.mockResolvedValue({});
    portalTarget.isConfigured.mockReturnValue(false);

    await service.handleParticipationEvent(tenant, message);

    expect(source.getJobDetails).toHaveBeenCalledWith(message.jobOpeningId, tenant);
    expect(source.getCandidateDetails).toHaveBeenCalled();
    expect(scoreTarget.importCandidate).toHaveBeenCalled();
    expect(portalTarget.importCandidate).not.toHaveBeenCalled();
  });

  it('should import candidate to both score and portal targets when portal is configured and portalProjectId is returned', async () => {
    const message = new EventMessage();
    message.id = 'PCPN_test_1';
    message.jobOpeningId = 'JOPG_test_1';
    const tenant: TenantConfig = { id: 1, name: 'test' } as TenantConfig;

    source.getJobDetails.mockResolvedValue({ id: 'test-job', source: {} });
    source.getCandidateDetails.mockResolvedValue(new Candidate('test-candidate'));
    scoreTarget.importCandidate.mockResolvedValue({
      participation: { id: 'test-participation-id', sovren: {} },
      portalProjectId: 'test-portal-project-id',
    });
    portalTarget.isConfigured.mockReturnValue(true);

    await service.handleParticipationEvent(tenant, message);

    expect(source.getJobDetails).toHaveBeenCalledWith(message.jobOpeningId, tenant);
    expect(source.getCandidateDetails).toHaveBeenCalled();
    expect(scoreTarget.importCandidate).toHaveBeenCalled();
    expect(portalTarget.importCandidate).toHaveBeenCalled();
  });

  it('should not import candidate to portal target when portal is configured but portalProjectId is not returned', async () => {
    const message = new EventMessage();
    message.id = 'PCPN_test_1';
    message.jobOpeningId = 'JOPG_test_1';
    const tenant: TenantConfig = { id: 1, name: 'test' } as TenantConfig;

    source.getJobDetails.mockResolvedValue({ id: 'test-job', source: {} });
    source.getCandidateDetails.mockResolvedValue(new Candidate('test-candidate'));
    scoreTarget.importCandidate.mockResolvedValue({});
    portalTarget.isConfigured.mockReturnValue(true);

    await service.handleParticipationEvent(tenant, message);

    expect(source.getJobDetails).toHaveBeenCalledWith(message.jobOpeningId, tenant);
    expect(source.getCandidateDetails).toHaveBeenCalled();
    expect(scoreTarget.importCandidate).toHaveBeenCalled();
    expect(portalTarget.importCandidate).not.toHaveBeenCalled();
  });
});
