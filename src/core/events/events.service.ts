import { Inject, Injectable, InternalServerErrorException } from '@nestjs/common';
import { EventsService } from '@core/events/events-service.interface';
import { OnEvent } from '@nestjs/event-emitter';
import { Source } from '@core/sync/source.interface';
import { Target } from '@core/sync/target.interface';
import { EventData, EventKind, EventMessage } from '@core/events/event-data.model';
import { Candidate } from '@core/sync/model';
import { PortalException } from '@EmmySoft-GmbH/emmysoft-client';
import { SoftfactorsException } from '@core/source/softfactors/softfactors.exception';
import { AxiosError } from 'axios';
import { TENANT_SERVICE, TenantService } from '@core/tenant/tenant-service.interface';
import { TenantConfig } from '@core/env.model';
import { ERROR_REPORTER, ErrorReporter } from '@core/error-reporter.interface';
import { SOFTFACTORS_SOURCE_SERVICE } from '@core/source/softfactors/softfactors-source.service';
import { SCORE_TARGET_SERVICE } from '@core/target/score/score-target.service';
import { EnhancedLoggerService } from '@infrastructure/logger/enhanced-logger.service';
import { PORTAL_V4_TARGET_SERVICE } from '@core/target/portal-v4/portal-v4-target.service';

// TODO - rename this to MoonwalkEventsService and move to source/moonwalk
@Injectable()
export class EventsServiceImpl implements EventsService {
  constructor(
    private readonly logger: EnhancedLoggerService,
    @Inject(SOFTFACTORS_SOURCE_SERVICE) private readonly moonwalk: Source,
    @Inject(SCORE_TARGET_SERVICE) private readonly score: Target,
    @Inject(PORTAL_V4_TARGET_SERVICE) private readonly portal: Target, // portal v5 is disabled for now
    @Inject(TENANT_SERVICE) private readonly tenantService: TenantService,
    @Inject(ERROR_REPORTER) private readonly errorReporter: ErrorReporter,
  ) {
    this.logger.setContext(EventsServiceImpl.name);
    if (!this.moonwalk || !this.score) {
      throw new InternalServerErrorException('Source &  target modules not configured');
    }
  }

  /**
   * Event handler for processing moonwalk events.
   * Handles both Position and Participation related events by routing them to their respective handlers.
   *
   * @decorator @OnEvent('moonwalk.event', { suppressErrors: false })
   *
   * @param {EventData} event - The event data object containing tenant and message information
   * Expected structure: { tenantId: string, message: { eventKind: string, ... } }
   *
   * @throws Will pass exceptions to handleException method if processing fails
   * @returns {Promise<void>} A promise that resolves when the event is processed
   *
   * @example
   * Event structure:
   * {
   * tenantId: "tenant123",
   * message: {
   * eventKind: "Position.Update",
   * // other event specific data
   * }
   * }
   */
  @OnEvent('moonwalk.event', { suppressErrors: false })
  public async handleEvent(event: EventData): Promise<void> {
    // Log the incoming event for debugging/tracking
    this.logger.info(
      'EventsServiceImpl.handleEvent: Source [%s] Tenant [%s] EventKind [%s] ID [%s]',
      event.sourceMetaName,
      event.tenantId,
      event.message.eventKind,
      event.message.id,
    );

    // Retrieve tenant configuration for the event
    const tenant = this.tenantService.getTenantConfig(event.tenantId);

    try {
      // Route position-related events (e.g., Position.Update, Position.Create)
      if (event.message.eventKind.startsWith('Position')) {
        await this.handlePositionEvent(tenant, event.message);
      }

      // Route participation-related events (e.g., Participation.Add, Participation.Remove)
      if (event.message.eventKind.startsWith('Participation')) {
        await this.handleParticipationEvent(tenant, event.message);
      }
    } catch (error) {
      // Handle any errors that occur during event processing
      this.handleException(error, event);
    }
  }

  async handlePositionEvent(tenant: TenantConfig, message: EventMessage): Promise<void> {
    switch (message.eventKind) {
      case EventKind.PositionDeleted:
        this.logger.info(`EVENT: Will delete job in score: ${message.id}:${message.eventKind}`);
        await this.score.deleteJob(new Candidate(message.id), this.moonwalk, tenant);
        this.logger.info(`EVENT: Job deleted in score ${message.id}:${message.eventKind}`);
        /*
        // For now we don't need to handle portal jobs
        if (this.portal.isConfigured()) {
          await this.portal.deleteJob(new Candidate(message.id), this.moonwalk, tenant);
        }
        */
        break;
      default:
        this.logger.info(`EVENT: Will import job to score: ${message.id}:${message.eventKind}`);
        const jobResponse = await this.importJob(this.moonwalk, this.score, tenant, message);
        this.logger.info(`EVENT: Job imported to score ${message.id}:${message.eventKind}`);

        if (this.portal.isConfigured() && jobResponse?.portalProjectId) {
          await this.importJob(this.moonwalk, this.portal, tenant, message, {
            portalProjectId: jobResponse.portalProjectId,
            scoreProjectIdId: jobResponse.jobOpening.id,
            softfactorsJobOpeningId: jobResponse.jobOpening.softfactorsData.id,
          });
        }
        break;
    }
  }

  async handleParticipationEvent(tenant: TenantConfig, message: EventMessage): Promise<void> {
    switch (message.eventKind) {
      case EventKind.ParticipationDeleted:
        await this.deleteCandidate(tenant, message);
        break;
      default:
        await this.importCandidate(tenant, message);
        break;
    }
  }

  private async importCandidate(tenant: TenantConfig, message: EventMessage): Promise<void> {
    const candidateResponse = await this.createOrUpdateCandidate(this.moonwalk, this.score, tenant, message);

    // verify if candidateResponse has property portalProjectId and that it has value before importing to portal
    if (this.portal.isConfigured() && candidateResponse?.portalProjectId) {
      await this.createOrUpdateCandidate(this.moonwalk, this.portal, tenant, message, {
        portalProjectId: candidateResponse.portalProjectId,
        scoreParticipationId: candidateResponse.participation.id, // this is score jobopeningparticipation entity
        sovren: candidateResponse.participation.sovren,
      });
    }
  }

  private async deleteCandidate(tenant: TenantConfig, message: EventMessage): Promise<void> {
    this.logger.info(`EVENT: Will delete candidate in score: ${message.id}:${message.eventKind}`);
    await this.score.deleteCandidate(new Candidate(message.id), this.moonwalk, tenant);
    if (this.portal.isConfigured()) {
      this.logger.info(`EVENT: Will delete candidate in portal: ${message.id}:${message.eventKind}`);
      await this.portal.deleteCandidate(new Candidate(message.id), this.moonwalk, tenant);
    }
  }

  private async importJob(
    source: Source,
    target: Target,
    tenant: TenantConfig,
    message: EventMessage,
    options?: Record<string, any>,
  ): Promise<any> {
    const job = await source.getJobDetails(message.id, tenant);

    const jobResponse = await target.importJob(job, source, tenant);
    this.logger.info(`EVENT: Imported job to ${target.getTargetMeta().name} -  ${message.id}:${message.eventKind}`);
    return jobResponse;
  }

  private async createOrUpdateCandidate(
    source: Source,
    target: Target,
    tenant: TenantConfig,
    message: EventMessage,
    options?: Record<string, any>,
  ): Promise<any> {
    const job = await source.getJobDetails(message.jobOpeningId, tenant);

    const candidate = await source.getCandidateDetails(message.jobOpeningId, new Candidate(message.id), tenant);

    // Initialize job with target job data (used in sync process) - target job data is in this case portal job data
    await target.initWithTargetJob(job, source, tenant);

    const candidateResponse = await target.importCandidate(candidate, job, source, tenant, options);
    this.logger.info(
      `EVENT: Imported participation to ${target.getTargetMeta().name} -  ${message.id}:${message.eventKind}`,
    );
    return candidateResponse;
  }

  private handleException(error: Error, event: EventData): void {
    this.logger.error(
      `EVENT: Failed to handle data ${event.message.id}:${event.message.eventKind} \n CAUSE: %o`,
      error,
    );
    this.logger.error(error.message, error.stack);

    if (error instanceof PortalException) {
      const portalError = error as PortalException;
      const cause = portalError.cause as AxiosError;
      this.logger.error(
        `EVENT: Failed to import data ${event.message.id}:${
          event.message.eventKind
        } \n STATUS: ${portalError.getStatus()}
          \n CAUSE: ${JSON.stringify(cause.response.data)}`,
        cause.stack,
      );
    } else if (error instanceof SoftfactorsException) {
      const softfactorsError = error as SoftfactorsException;
      const cause = softfactorsError.cause as AxiosError;
      this.logger.error(
        `EVENT: Failed to fetch softfactors data ${event.message.id}:${
          event.message.eventKind
        } \n STATUS: ${softfactorsError.getStatus()}
          \n CAUSE: ${JSON.stringify(cause.response.data)}`,
        cause.stack,
      );
    } else {
      this.logger.error(
        `EVENT: Failed to handle data ${event.message.id}:${event.message.eventKind} \n CAUSE: ${JSON.stringify(
          error,
        )}`,
        error.stack,
      );
    }
    this.errorReporter.report(error, event.tenantId, { ...event.message });
    throw error;
  }
}
